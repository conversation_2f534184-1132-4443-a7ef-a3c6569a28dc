{"name": "backend", "version": "1.0.0", "private": true, "main": "src/api.js", "type": "commonjs", "scripts": {"start": "node src/api.js", "dev": "nodemon src/api.js", "test": "jest"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@moralisweb3/common-evm-utils": "^2.27.2", "@solana/web3.js": "^1.98.2", "axios": "^1.9.0", "request": "^2.88.2", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "ethers": "^6.14.3", "execp": "^0.0.1", "express": "^5.1.0", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "mongoose-validator": "^2.1.0", "moralis": "^2.27.2", "path": "^0.12.7", "pug": "^3.0.3", "web3": "^4.16.0"}, "devDependencies": {"jest": "^29.0.0", "mongodb-memory-server": "^10.1.4", "nodemon": "^3.0.2"}, "jest": {"testEnvironment": "node"}}