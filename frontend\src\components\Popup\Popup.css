.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(27, 31, 36, 0.8); 
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.popup {
  background-color: #1a1d20;
  color: white;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  border-radius: 16px;
  text-align: center;
  font-family: sans-serif;
  position: relative;
  display: flex;
  flex-direction: column;
}

.popup input{
  border: 1px solid #4B5563; 
  border-radius: 6px;
  margin-top: 1em;
  height: 2.5em;
  background: #2D333B;
  color: white;
} 

.close-button {
  width: 1.5em;
  height: 1.5em;
  margin-bottom: 1em;  
  background: none;
  border: none;
  font-size: 1.5rem;
  color: white;
  align-items: center;
  justify-content: center;
}

.popup h2 {
  font-size: 1.5rem;
  font-family: sans-serif;
  margin: 0;
  margin-bottom: 1em;  
  color: #ffffff;
}

.popup .btn {
  border: none;
  font-weight: bold;
  cursor: pointer;
  background-color: #2D333B;
  color: white;
  margin-top: 1em;
}

.popup .btn:hover{
  color: #2D333B;
}