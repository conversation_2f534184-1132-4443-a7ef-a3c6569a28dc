.profile-page {
  background-color: #1B1F24;
  color: white;
  font-family: sans-serif;
  min-height: 100vh; 
  display: flex;
  flex-direction: column;
  padding-bottom: 2em;
}
.profile-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
  background-image: url('../../assets/images/bussinessmen-office.png');
  background-size: cover;
  background-position: center;
  height: 20em;
  position: relative;
}

.profile-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(27, 31, 36, 0.7);
  z-index: 1;
}

.profile-header h1 {
  color: white;
  font-size: 5.5rem;
  margin: 0;
  position: relative;
  z-index: 2;
}

.back-btn {
  position: absolute;
  top: 1em;
  left: 1em;
  padding: 1em 1em;
  background-color: rgba(27, 31, 36, 0.7);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  z-index: 2;
}

.back-btn:hover {
  background-color: rgba(27, 31, 36, 0.9);
}

.preferences-section,
.account-section {
  background-color: #121417;
  border-radius: 16px;
  padding: 2rem;
  margin: 3rem;
}

.preferences-section h2,
.account-section h2 {
  color: #F9B64D;
  font-size: 2em;
  margin-bottom: 3.5rem;
}

.preference-item label {
  color: #D1D5DB;
  text-transform: capitalize;
  font-size: 1.2em;
  font-weight: 700;
  white-space: nowrap;
}


input[type='range']::-webkit-slider-thumb {
  width: 12px;
  -webkit-appearance: none;
  height: 16px;
  background: white;
  border-radius: 2px;
}

.preference-item input[type="range"] {
  width: 100%;
  margin-bottom: 4em;
  appearance: none;
  background: #F9B64D;
  height: 0.8em;
  border: none;
  border-radius: 2px;
}

.preference-item input[type="range"]::-moz-range-thumb {
  width: 12px;
  height: 16px;
  background: white;
  border-radius: 2px;
}

.profile-page input{
  border: 1px solid #4B5563; 
  border-radius: 6px;
  margin-top: 1em;
  height: 2.5em;
  background: #2D333B;
  color: white;
} 

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s;
  background-color: #F9B64D;
  color: black;
}

.btn:hover {
  background-color: #FBBF24;
}

.delete-btn {
  background-color: #DC2626;
  color: white;
}

.delete-btn:hover {
  background-color: #B91C1C;
}

.account-buttons {
  display: flex;
  gap: 1rem;
  max-width: 50em;
}

.account-buttons .btn {
  flex: 1;
  white-space: nowrap;
}

@media screen and (max-width: 768px) {
  .account-buttons {
    flex-direction: column;
    width: 100%;
  }

  .account-buttons .btn {
    width: 100%;
  }
}
