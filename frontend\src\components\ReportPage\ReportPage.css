.report-page {
  background-color: #1B1F24;
  min-height: 100vh;
  color: white;
  font-family: sans-serif;
}

.report-header {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('../../assets/images/banker-thinking.png');
  background-size: cover;
  background-position: center;
  height: 15em;
  position: relative;
}

.report-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(27, 31, 36, 0.7);
  z-index: 1;
}

.report-header h1 {
  color: white;
  font-size: 5.5rem;
  margin: 0;
  position: relative;
  z-index: 2;
}

.back-btn {
  position: absolute;
  top: 1em;
  left: 1em;
  padding: 1em 1em;
  background-color: rgba(27, 31, 36, 0.7);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  z-index: 2;
}

.report-form {
  padding: 1em;
  gap: 1em;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #121417;
}

.report-form input {
  flex: 3;
  padding: 0.75rem;
  border: 1px solid #2C2F36;
  border-radius: 8px;
  background-color: #2C2F36;
  color: white;
  font-size: 1rem;
}

.report-form select {
  flex: 1;
  cursor: pointer;
  padding: 0.75rem;
  border: 1px solid #2C2F36;
  border-radius: 8px;
  background-color: #2C2F36;
  color: white;
  font-size: 1rem;
  height: 42px;
  min-width: 120px;
}

.btn-primary {
  flex: 1;
  height: 42px;
  min-width: 100px;
  max-width: 120px;
}

@media(max-width: 768px) {
  .report-form {
    flex-direction: column;
    padding: 16px;
    margin: 16px;
  }

  .report-form input,
  .report-form select,
  .btn-primary {
    width: 100%;
    max-width: none;
  }
}

.error {
  color: #f87171;
  text-align: center;
}

.report-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  background-color: #1B1F24;
  border: 1px solid #4B5563;
  border-radius: 16px;
  box-shadow: 0 10px 15px rgba(0,0,0,0.3);
}

.report-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media(min-width: 768px) {
  .report-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.report-card {
  background-color: #2C2F36;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.2);
}

.report-card h3 {
  margin-bottom: 12px;
  font-size: 1.25rem;
  color: #F9B64D;
}

.report-card p {
  margin: 4px 0;
}

.insights {
  margin-top: 32px;
}

.insights h3 {
  font-size: 1.5rem;
  margin-bottom: 16px;
  color: #F9B64D;
}

.insights p {
  color: #D1D5DB;
  line-height: 1.5;
}