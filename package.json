{"name": "my-banker", "private": true, "version": "1.0.0", "workspaces": ["frontend", "backend"], "scripts": {"start:frontend": "npm --workspace frontend start", "start:backend": "npm --workspace backend start", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\""}, "devDependencies": {"autoprefixer": "^10.4.21", "concurrently": "^8.2.0", "postcss": "^8.5.4", "supertest": "^7.1.1", "tailwindcss": "^3.4.17"}, "dependencies": {"@fortawesome/react-fontawesome": "^0.2.2", "jwt-decode": "^4.0.0", "autoprefixer": "^10.4.21", "axios": "^1.4.0", "execp": "^0.0.1", "fs": "^0.0.1-security", "path": "^0.12.7", "postcss": "^8.5.4", "request": "^2.88.2", "tailwindcss": "^3.4.17"}}